"""
Script final para extrair dataset_ids de TODOS os PDFs.
"""

import pandas as pd
import re
import random
from pathlib import Path
from pypdf import PdfReader
from tqdm import tqdm


def normalize_dataset_id(dataset_id):
    """Normaliza o dataset_id para diferentes variações."""
    if dataset_id == "Missing":
        return []
    
    variations = [dataset_id]  # Versão completa
    
    # Se começar com https://, adicionar versão sem https://
    if dataset_id.startswith("https://"):
        without_https = dataset_id.replace("https://", "")
        variations.append(without_https)
        
        # Se contém doi.org, adicionar apenas a parte após doi.org/
        if "doi.org/" in without_https:
            after_doi = without_https.split("doi.org/", 1)[1]
            variations.append(after_doi)

             # Adicionar versão com doi: prefix
            variations.append(f"doi:{dataset_id}")
            variations.append(f"doi: {dataset_id}")
    
    # Se não começar com https:// mas contém doi.org, adicionar a parte após doi.org/
    elif "doi.org/" in dataset_id:
        after_doi = dataset_id.split("doi.org/", 1)[1]
        variations.append(after_doi)
    
    return variations


def extract_preceding_words(text, target, num_words=10):
    """Extrai as palavras que antecedem um target no texto."""
    # Escapar caracteres especiais para regex
    escaped_target = re.escape(target)
    
    # Procurar o target no texto (case insensitive)
    pattern = rf'(.+?)\b{escaped_target}\b'
    match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
    
    if match:
        preceding_text = match.group(1)
        # Dividir em palavras e pegar as últimas num_words
        words = re.findall(r'\b\w+\b', preceding_text)
        if len(words) >= num_words:
            return ' '.join(words[-num_words:])
        else:
            return ' '.join(words)
    
    return None


def extract_random_words(text, num_words=10):
    """Extrai num_words palavras consecutivas aleatórias do texto."""
    words = re.findall(r'\b\w+\b', text)
    
    if len(words) < num_words:
        return ' '.join(words)
    
    # Escolher posição inicial aleatória
    start_pos = random.randint(0, len(words) - num_words)
    return ' '.join(words[start_pos:start_pos + num_words])


def process_pdf(article_id, dataset_id, data_dir="data"):
    """Processa um PDF individual."""
    pdf_path = Path(data_dir) / "train" / "PDF" / f"{article_id}.pdf"
    
    if not pdf_path.exists():
        return article_id, dataset_id, "PDF_NOT_FOUND"
    
    try:
        # Carregar PDF
        reader = PdfReader(pdf_path)
        
        # Extrair texto de todas as páginas
        full_text = ""
        for page in reader.pages:
            full_text += page.extract_text() + "\n"
        
        if dataset_id == "Missing":
            # Para casos Missing, extrair 10 palavras aleatórias
            random_words = extract_random_words(full_text)
            return article_id, dataset_id, random_words
        
        # Tentar encontrar o dataset_id e suas variações
        variations = normalize_dataset_id(dataset_id)
        
        for variation in variations:
            preceding_words = extract_preceding_words(full_text, variation)
            if preceding_words:
                return article_id, dataset_id, preceding_words
        
        # Se não encontrou nenhuma variação
        return article_id, dataset_id, "DATASET_ID_NOT_FOUND"
        
    except Exception as e:
        return article_id, dataset_id, f"ERROR: {str(e)}"


def main():
    """Função principal para processar TODOS os PDFs."""
    print("=== Extração Completa de Dataset IDs ===")
    print("Este script processará TODOS os PDFs do dataset.")
    print("Isso pode levar bastante tempo (estimativa: 30-60 minutos).\n")
    
    # Carregar dados
    labels_df = pd.read_csv("data/train_labels.csv")
    total_files = len(labels_df)
    
    print(f"Total de entradas a processar: {total_files}")
    
    # Confirmar se o usuário quer continuar
    response = input("Deseja continuar? (s/n): ").strip().lower()
    if response not in ['s', 'sim', 'y', 'yes']:
        print("Processamento cancelado.")
        return
    
    print(f"\nIniciando processamento de {total_files} entradas...")
    
    results = []
    
    # Usar tqdm para barra de progresso
    for idx, row in tqdm(labels_df.iterrows(), total=total_files, desc="Processando PDFs"):
        article_id = row['article_id']
        dataset_id = row['dataset_id']
        
        result = process_pdf(article_id, dataset_id)
        results.append(result)
        
        # Salvar resultados parciais a cada 100 arquivos
        if (idx + 1) % 10 == 0:
            partial_df = pd.DataFrame(results, columns=[
                'article_id', 'dataset_id', 'preceding_words'
            ])
            
            results_dir = Path("results")
            results_dir.mkdir(exist_ok=True)
            
            partial_path = results_dir / f"partial_extraction.csv"
            partial_df.to_csv(partial_path, index=False, encoding='utf-8')
            print(f"\nSalvamento parcial: {partial_path}")
    
    # Criar DataFrame final com resultados
    results_df = pd.DataFrame(results, columns=[
        'article_id', 'dataset_id', 'preceding_words'
    ])
    
    # Salvar resultados finais
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    output_path = results_dir / "complete_pdf_extraction_results.csv"
    results_df.to_csv(output_path, index=False, encoding='utf-8')
    
    print(f"\n✅ Resultados finais salvos em: {output_path}")
    
    # Mostrar estatísticas finais
    total = len(results_df)
    found = len(results_df[~results_df['preceding_words'].str.contains('NOT_FOUND|ERROR', na=False)])
    missing = len(results_df[results_df['dataset_id'] == 'Missing'])
    not_found = total - found
    
    print(f"\n📊 Estatísticas Finais:")
    print(f"Total de entradas: {total}")
    print(f"Dataset IDs encontrados: {found - missing} ({((found - missing) / (total - missing) * 100):.1f}% dos não-Missing)")
    print(f"Casos Missing: {missing}")
    print(f"Não encontrados/Erros: {not_found}")
    
    # Mostrar alguns exemplos de sucessos
    success_cases = results_df[
        (~results_df['preceding_words'].str.contains('NOT_FOUND|ERROR', na=False)) &
        (results_df['dataset_id'] != 'Missing')
    ]
    
    if len(success_cases) > 0:
        print(f"\n🎯 Exemplos de Dataset IDs encontrados:")
        for idx, row in success_cases.head(5).iterrows():
            print(f"  📄 {row['article_id']}")
            print(f"  🔗 {row['dataset_id']}")
            print(f"  📝 {row['preceding_words']}")
            print()
    
    print("🎉 Processamento completo finalizado!")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  Processamento interrompido pelo usuário.")
        print("Os resultados parciais foram salvos na pasta 'results/'.")
    except Exception as e:
        print(f"\n❌ Erro durante o processamento: {e}")
        import traceback
        traceback.print_exc()
