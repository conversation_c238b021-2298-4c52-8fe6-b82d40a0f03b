"""
Script final para extrair dataset_ids de TODOS os PDFs.
"""

import pandas as pd
import re
import random
from pathlib import Path
from pypdf import PdfReader
from tqdm import tqdm


def clean_text_for_urls(text):
    """Clean text to handle URLs split across lines and other formatting issues."""
    # Fix common URL splits
    text = re.sub(r'https://doi\.\s*\n\s*org/', 'https://doi.org/', text)
    text = re.sub(r'doi\.org/\s*\n\s*', 'doi.org/', text)
    text = re.sub(r'https://doi\.\s+org/', 'https://doi.org/', text)
    text = re.sub(r'10\.\d+/\s*\n\s*', '10.', text)

    # Fix dryad URLs specifically
    text = re.sub(r'dryad\.\s*\n\s*', 'dryad.', text)
    text = re.sub(r'10\.5061/\s*\n\s*dryad\.', '10.5061/dryad.', text)

    # Fix other common splits in dataset IDs
    text = re.sub(r'(\w+)\s*\n\s*(\w+)', r'\1\2', text)
    text = re.sub(r'(\d+)\s*\n\s*(\d+)', r'\1\2', text)

    # Remove excessive whitespace but preserve single spaces
    text = re.sub(r'\s+', ' ', text)

    return text


def normalize_dataset_id(dataset_id):
    """Normaliza o dataset_id para diferentes variações."""
    if dataset_id == "Missing":
        return []

    variations = [dataset_id]  # Versão completa

    # Se começar com https://, adicionar versão sem https://
    if dataset_id.startswith("https://"):
        without_https = dataset_id.replace("https://", "")
        variations.append(without_https)

        # Se contém doi.org, adicionar apenas a parte após doi.org/
        if "doi.org/" in without_https:
            after_doi = without_https.split("doi.org/", 1)[1]
            variations.append(after_doi)

        # Adicionar versão com doi: prefix
        variations.append(f"doi:{dataset_id}")
        variations.append(f"doi: {dataset_id}")
        variations.append(f"doi:https://{without_https}")

    # Se não começar com https:// mas contém doi.org, adicionar a parte após doi.org/
    elif "doi.org/" in dataset_id:
        after_doi = dataset_id.split("doi.org/", 1)[1]
        variations.append(after_doi)
        variations.append(f"https://{dataset_id}")
        variations.append(f"doi:{dataset_id}")

    # Adicionar variações específicas para DOIs
    if "10." in dataset_id:
        # Extrair apenas a parte do DOI
        doi_match = re.search(r'10\.\d+/[\w\d\.-]+', dataset_id)
        if doi_match:
            doi_part = doi_match.group()
            variations.append(doi_part)
            variations.append(f"doi:{doi_part}")
            variations.append(f"doi: {doi_part}")

    # Adicionar variações para dryad específicamente
    if "dryad" in dataset_id.lower():
        # Extrair apenas o identificador após dryad.
        dryad_match = re.search(r'dryad\.(\w+)', dataset_id, re.IGNORECASE)
        if dryad_match:
            dryad_id = dryad_match.group(1)
            variations.append(dryad_id)
            variations.append(f"dryad.{dryad_id}")

    # Remover duplicatas mantendo ordem
    seen = set()
    unique_variations = []
    for var in variations:
        if var not in seen:
            seen.add(var)
            unique_variations.append(var)

    return unique_variations


def extract_preceding_words(text, target, num_words=10):
    """Extrai as palavras que antecedem um target no texto com busca robusta."""
    # Clean text first
    cleaned_text = clean_text_for_urls(text)

    # Try exact match first
    try:
        escaped_target = re.escape(target)
        pattern = rf'(.+?)\b{escaped_target}\b'
        match = re.search(pattern, cleaned_text, re.IGNORECASE | re.DOTALL)

        if match:
            preceding_text = match.group(1)
            words = re.findall(r'\b\w+\b', preceding_text)
            if len(words) >= num_words:
                return ' '.join(words[-num_words:])
            else:
                return ' '.join(words)
    except Exception:
        pass

    # Try partial matching for DOIs and URLs
    if "doi.org/" in target or target.startswith("10.") or "dryad" in target.lower():
        try:
            # Extract the key part for searching
            if "doi.org/" in target:
                key_part = target.split("doi.org/", 1)[1]
            elif target.startswith("10."):
                key_part = target
            elif "dryad" in target.lower():
                # Extract dryad identifier
                dryad_match = re.search(r'dryad\.(\w+)', target, re.IGNORECASE)
                if dryad_match:
                    key_part = dryad_match.group(1)
                else:
                    key_part = target
            else:
                key_part = target

            # Clean key part
            key_clean = re.sub(r'[^\w/.-]', '', key_part)

            if key_clean:
                escaped_key = re.escape(key_clean)
                pattern = rf'(.+?)\b{escaped_key}\b'
                match = re.search(pattern, cleaned_text, re.IGNORECASE | re.DOTALL)

                if match:
                    preceding_text = match.group(1)
                    words = re.findall(r'\b\w+\b', preceding_text)
                    if len(words) >= num_words:
                        return ' '.join(words[-num_words:])
                    else:
                        return ' '.join(words)
        except Exception:
            pass

    return None


def search_in_sections(text, target, sections=None):
    """Busca o target em seções específicas do texto."""
    if sections is None:
        sections = [
            "DATA AVAILABILITY STATEMENT",
            "DATA AVAILABILITY",
            "DATA ACCESSIBILITY",
            "ACKNOWLEDGMENT",
            "ACKNOWLEDGMENTS",
            "SUPPORTING INFORMATION",
            "SUPPLEMENTARY MATERIAL"
        ]

    cleaned_text = clean_text_for_urls(text)

    for section in sections:
        # Find section in text
        section_pattern = rf'{re.escape(section)}(.{{0,2000}}?)(?:REFERENCES|CONFLICT|AUTHOR|FUNDING|\n\n[A-Z]{{3,}}|\Z)'
        section_match = re.search(section_pattern, cleaned_text, re.IGNORECASE | re.DOTALL)

        if section_match:
            section_text = section_match.group(1)
            # Try to find target in this section
            preceding_words = extract_preceding_words(section_text, target)
            if preceding_words:
                return preceding_words

    return None


def extract_random_words(text, num_words=10):
    """Extrai num_words palavras consecutivas aleatórias do texto."""
    words = re.findall(r'\b\w+\b', text)

    if len(words) < num_words:
        return ' '.join(words)

    # Escolher posição inicial aleatória
    start_pos = random.randint(0, len(words) - num_words)
    return ' '.join(words[start_pos:start_pos + num_words])


def extract_random_words_from_page(page_text, num_words=10):
    """Extrai num_words palavras consecutivas aleatórias de uma página."""
    words = re.findall(r'\b\w+\b', page_text)

    if len(words) < num_words:
        return ' '.join(words)

    # Escolher posição inicial aleatória
    start_pos = random.randint(0, len(words) - num_words)
    return ' '.join(words[start_pos:start_pos + num_words])


def search_in_page_text(page_text, variations):
    """Busca as variações do dataset_id em uma página específica."""
    # Primeira tentativa: busca em seções específicas da página
    for variation in variations:
        preceding_words = search_in_sections(page_text, variation)
        if preceding_words:
            return f"SECTION: {preceding_words}"

    # Segunda tentativa: busca no texto completo da página
    for variation in variations:
        preceding_words = extract_preceding_words(page_text, variation)
        if preceding_words:
            return preceding_words

    # Terceira tentativa: busca por padrões parciais
    cleaned_text = clean_text_for_urls(page_text)
    for variation in variations:
        # Try to find any part of the dataset ID
        if len(variation) > 10:  # Only for longer IDs
            parts = re.split(r'[/\.]', variation)
            for part in parts:
                if len(part) > 5:  # Only meaningful parts
                    if part in cleaned_text:
                        preceding_words = extract_preceding_words(cleaned_text, part)
                        if preceding_words:
                            return f"PARTIAL: {preceding_words}"

    return None


def process_pdf(article_id, dataset_id, data_dir="data"):
    """Processa um PDF individual com busca otimizada página por página."""
    pdf_path = Path(data_dir) / "train" / "PDF" / f"{article_id}.pdf"

    if not pdf_path.exists():
        return article_id, dataset_id, "PDF_NOT_FOUND"

    try:
        # Carregar PDF
        reader = PdfReader(pdf_path)

        if dataset_id == "Missing":
            # Para casos Missing, pegar uma página aleatória e extrair 10 palavras
            if len(reader.pages) > 0:
                random_page_idx = random.randint(0, len(reader.pages) - 1)
                try:
                    page_text = reader.pages[random_page_idx].extract_text()
                    random_words = extract_random_words_from_page(page_text)
                    return article_id, dataset_id, random_words
                except Exception:
                    return article_id, dataset_id, "ERROR_EXTRACTING_RANDOM_PAGE"
            else:
                return article_id, dataset_id, "PDF_NO_PAGES"

        # Para dataset_ids não-Missing, processar página por página com priorização
        variations = normalize_dataset_id(dataset_id)
        total_pages = len(reader.pages)

        # Estratégia de busca otimizada:
        # 1. Últimas 5 páginas (onde geralmente estão referências e data availability)
        # 2. Primeiras 3 páginas (abstract, introduction)
        # 3. Páginas restantes (máximo 10 páginas adicionais)

        pages_to_check = []

        # Últimas 5 páginas (mais prováveis de ter dataset info)
        if total_pages > 5:
            pages_to_check.extend(range(total_pages - 5, total_pages))
        else:
            pages_to_check.extend(range(total_pages))

        # Primeiras 3 páginas
        pages_to_check.extend(range(min(3, total_pages)))

        # Páginas do meio (máximo 10 páginas adicionais)
        if total_pages > 8:  # Se há páginas além das primeiras 3 e últimas 5
            middle_start = 3
            middle_end = total_pages - 5
            middle_pages = list(range(middle_start, middle_end))
            # Pegar no máximo 10 páginas do meio, priorizando as do final
            if len(middle_pages) > 10:
                middle_pages = middle_pages[-10:]
            pages_to_check.extend(middle_pages)

        # Remover duplicatas e ordenar
        pages_to_check = sorted(set(pages_to_check))

        # Processar páginas selecionadas
        for page_idx in pages_to_check:
            try:
                page_text = reader.pages[page_idx].extract_text()
                if not page_text.strip():  # Skip empty pages
                    continue

                # Buscar o dataset_id nesta página
                result = search_in_page_text(page_text, variations)
                if result:
                    return article_id, dataset_id, f"PAGE_{page_idx+1}: {result}"

            except Exception:
                # Se houver erro em uma página, continuar para a próxima
                continue

        # Se não encontrou em nenhuma página
        return article_id, dataset_id, "DATASET_ID_NOT_FOUND"

    except Exception as e:
        return article_id, dataset_id, f"ERROR: {str(e)}"


def main():
    """Função principal para processar TODOS os PDFs."""
    print("=== Extração Completa de Dataset IDs (Otimizada) ===")
    print("Este script processará TODOS os PDFs do dataset com busca página por página.")
    print("Estimativa de tempo: 5-15 minutos (muito mais rápido!).\n")
    
    # Carregar dados
    labels_df = pd.read_csv("data/train_labels.csv")
    total_files = len(labels_df)
    
    print(f"Total de entradas a processar: {total_files}")
    
    # Confirmar se o usuário quer continuar
    response = input("Deseja continuar? (s/n): ").strip().lower()
    if response not in ['s', 'sim', 'y', 'yes']:
        print("Processamento cancelado.")
        return
    
    print(f"\nIniciando processamento de {total_files} entradas...")
    
    results = []
    
    # Usar tqdm para barra de progresso
    for idx, row in tqdm(labels_df.iterrows(), total=total_files, desc="Processando PDFs"):
        article_id = row['article_id']
        dataset_id = row['dataset_id']
        
        result = process_pdf(article_id, dataset_id)
        results.append(result)
        
        # Salvar resultados parciais a cada 50 arquivos (mais frequente devido à velocidade)
        if (idx + 1) % 50 == 0:
            partial_df = pd.DataFrame(results, columns=[
                'article_id', 'dataset_id', 'preceding_words'
            ])
            
            results_dir = Path("results")
            results_dir.mkdir(exist_ok=True)
            
            partial_path = results_dir / f"partial_extraction.csv"
            partial_df.to_csv(partial_path, index=False, encoding='utf-8')
            print(f"\nSalvamento parcial: {partial_path}")
    
    # Criar DataFrame final com resultados
    results_df = pd.DataFrame(results, columns=[
        'article_id', 'dataset_id', 'preceding_words'
    ])
    
    # Salvar resultados finais
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    output_path = results_dir / "complete_pdf_extraction_results.csv"
    results_df.to_csv(output_path, index=False, encoding='utf-8')
    
    print(f"\n✅ Resultados finais salvos em: {output_path}")
    
    # Mostrar estatísticas finais
    total = len(results_df)
    found = len(results_df[~results_df['preceding_words'].str.contains('NOT_FOUND|ERROR', na=False)])
    missing = len(results_df[results_df['dataset_id'] == 'Missing'])
    not_found = total - found
    
    print(f"\n📊 Estatísticas Finais:")
    print(f"Total de entradas: {total}")
    print(f"Dataset IDs encontrados: {found - missing} ({((found - missing) / (total - missing) * 100):.1f}% dos não-Missing)")
    print(f"Casos Missing: {missing}")
    print(f"Não encontrados/Erros: {not_found}")
    
    # Mostrar alguns exemplos de sucessos
    success_cases = results_df[
        (~results_df['preceding_words'].str.contains('NOT_FOUND|ERROR', na=False)) &
        (results_df['dataset_id'] != 'Missing')
    ]
    
    if len(success_cases) > 0:
        print(f"\n🎯 Exemplos de Dataset IDs encontrados:")
        for idx, row in success_cases.head(5).iterrows():
            print(f"  📄 {row['article_id']}")
            print(f"  🔗 {row['dataset_id']}")
            print(f"  📝 {row['preceding_words']}")
            print()
    
    print("🎉 Processamento completo finalizado!")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  Processamento interrompido pelo usuário.")
        print("Os resultados parciais foram salvos na pasta 'results/'.")
    except Exception as e:
        print(f"\n❌ Erro durante o processamento: {e}")
        import traceback
        traceback.print_exc()
